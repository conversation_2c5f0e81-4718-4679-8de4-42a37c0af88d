version: '3'

networks:
  development:
    name: vagrant_development
  staging:
    name: vagrant_staging
  production:
    name: vagrant_production
services:
  vault-development:
    container_name: vault-development
    networks:
      - development
    image: hashicorp/vault:1.19
    cap_add:
      - IPC_LOCK
    ports:
      - "8201:8200"
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=f23612cf-824d-4206-9e94-e31a6dc8ee8d
  vault-staging:
    container_name: vault-staging
    networks:
      - staging
    image: hashicorp/vault:1.19
    cap_add:
      - IPC_LOCK
    ports:
      - "8202:8200"
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=staging-vault-token-12345
  vault-production:
    container_name: vault-production
    networks:
      - production
    image: hashicorp/vault:1.19
    cap_add:
      - IPC_LOCK
    ports:
      - "8301:8200"
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=083672fc-4471-4ec4-9b59-a285e463a973
