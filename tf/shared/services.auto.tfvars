# Shared service definitions that can be used across all environments
# This file is symlinked to each environment directory

services = {
  account = {
    image         = "form3tech-oss/platformtest-account"
    vault_enabled = true
  }
  gateway = {
    image         = "form3tech-oss/platformtest-gateway"
    vault_enabled = true
  }
  payment = {
    image         = "form3tech-oss/platformtest-payment"
    vault_enabled = true
  }
  frontend = {
    image            = "docker.io/nginx:latest"
    vault_enabled    = false
    production_image = "docker.io/nginx:1.22.0-alpine"
  }
}
