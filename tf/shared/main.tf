############ MODULES ############
module "vault_environment" {
  source                 = "../../modules/vault-environment"
  environment_name       = var.environment_name
  environment_config     = var.environment_config
  services               = var.services
  vault_enabled_services = {
    for service_name, service_config in var.services : service_name => service_config
    if service_config.vault_enabled
  }
}

############ TERRAFORM CONFIGURATION ############
terraform {
  required_version = ">= 1.0"

  # Required providers - versions should match module requirements
  required_providers {
    vault = {
      source  = "hashicorp/vault"
      version = "3.0.1"
    }
    docker = {
      source  = "kreuzwerker/docker"
      version = "2.15.0"
    }
  }

  # Backend configuration is defined in each environments main.tf
}
