# Shared variables definitions that can be used across all environments
# This file is symlinked to each environment directory

variable "environment_name" {
  description = "Name of the environment"
  type        = string
  default     = "staging"
}

variable "environment_config" {
  description = "Configuration for the environment"
  type = object({
    vault_address = string
    vault_token   = string
    network_name  = string
    frontend_port = number
    vault_host    = string
  })
}

variable "services" {
  description = "Configuration for all services"
  type = map(object({
    image            = string
    vault_enabled    = bool
    production_image = optional(string)
  }))
}
