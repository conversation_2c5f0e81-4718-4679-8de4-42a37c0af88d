# Variables for the vault environment module

variable "environment_name" {
  description = "Name of the environment (development, staging, production)"
  type        = string
}

variable "environment_config" {
  description = "Configuration for the environment"
  type = object({
    vault_address = string
    vault_token   = string
    network_name  = string
    frontend_port = number
    vault_host    = string
  })
}

variable "services" {
  description = "Map of services configuration"
  type = map(object({
    image         = string
    vault_enabled = bool
    production_image = optional(string)
  }))
}

variable "vault_enabled_services" {
  description = "Map of vault-enabled services for this environment"
  type = map(object({
    image         = string
    vault_enabled = bool
    production_image = optional(string)
  }))
}
