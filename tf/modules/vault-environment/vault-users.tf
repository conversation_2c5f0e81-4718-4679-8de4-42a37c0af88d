# Vault user endpoints for all vault-enabled services in this environment

resource "vault_generic_endpoint" "service_users" {
  for_each = var.vault_enabled_services
  
  depends_on           = [vault_auth_backend.userpass]
  path                 = "auth/userpass/users/${each.key}-${var.environment_name}"
  ignore_absent_fields = true

  data_json = jsonencode({
    policies = ["${each.key}-${var.environment_name}"]
    password = "123-${each.key}-${var.environment_name}"
  })
}
