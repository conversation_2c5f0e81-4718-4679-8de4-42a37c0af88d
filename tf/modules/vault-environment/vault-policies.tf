# Vault policies for all vault-enabled services in this environment

data "vault_policy_document" "service_policy_data" {
  for_each = var.vault_enabled_services
  rule {
    path         = "secret/data/${var.environment_name}/${each.key}"
    capabilities = ["list", "read"]
  }
}

resource "vault_policy" "service_policies" {
  for_each = var.vault_enabled_services
  name     = "${each.key}-${var.environment_name}"

  policy   = data.vault_policy_document.service_policy_data[each.key].hcl
}
