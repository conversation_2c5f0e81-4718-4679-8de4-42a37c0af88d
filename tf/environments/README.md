# Environment-Specific Terraform Configurations

This directory contains separate Terraform configurations for each environment, following best practices for infrastructure as code.

## Directory Structure (Simplified)

```
tf/environments/
├── development/
│   ├── main.tf              # Backend configuration only
│   ├── terraform.tfvars     # Environment-specific values
│   ├── shared_main.tf       # Symlink to ../shared/main.tf
│   ├── variables.tf         # Symlink to ../shared/variables.tf
│   ├── providers.tf         # Symlink to ../shared/providers.tf
│   └── services.auto.tfvars # Symlink to ../shared/services.auto.tfvars
├── staging/
│   ├── main.tf              # Backend configuration only
│   ├── terraform.tfvars     # Environment-specific values
│   └── [symlinks to shared files...]
├── production/
│   ├── main.tf              # Backend configuration only
│   ├── terraform.tfvars     # Environment-specific values
│   └── [symlinks to shared files...]
└── README.md               # This file
```

## Shared Configuration

Common configuration is centralized in `../shared/`:
- `main.tf` - Main terraform configuration and module calls
- `providers.tf` - Provider configurations
- `variables.tf` - Variable definitions
- `services.auto.tfvars` - Service configurations

## Benefits of This Structure

1. **No Code Duplication**: Shared configuration prevents drift and reduces maintenance
2. **Single Source of Truth**: Changes to providers, variables, or modules only need to be made once
3. **Independent Deployments**: Each environment can still be deployed separately
4. **Separate State Files**: No risk of accidentally affecting other environments
5. **Environment-Specific Configuration**: Each environment has its own variables and settings

## Usage

Navigate to the desired environment directory and run terraform commands:

```bash
cd development/
terraform init
terraform plan
terraform apply
```

## Available Environments

- `development/` - Development environment (port 4080, vault 8201)
- `staging/` - Staging environment (port 4082, vault 8202)
- `production/` - Production environment (port 4081, vault 8301)

## Usage

### Deploy Development Environment
```bash
cd tf/environments/development
terraform init
terraform plan
terraform apply
```

### Deploy Staging Environment
```bash
cd tf/environments/staging
terraform init
terraform plan
terraform apply
```

### Deploy Production Environment
```bash
cd tf/environments/production
terraform init
terraform plan
terraform apply
```

## Configuration

Each environment has its own `terraform.tfvars` file with environment-specific values:

- **Vault addresses and tokens**
- **Network names**
- **Port configurations**
- **Service configurations**

## Adding New Environments

1. Create a new directory under `tf/environments/`
2. Copy the files from an existing environment
3. Update the `terraform.tfvars` file with new environment values
4. Update the backend configuration in `main.tf` if using remote state

## CI/CD Integration

Each environment should have its own CI/CD pipeline:

```yaml
# Example GitLab CI structure
stages:
  - validate
  - plan-dev
  - apply-dev

validate:
  script:
    - terraform fmt -check -recursive
    - terraform validate

plan-dev:
  script:
    - cd tf/environments/development
    - terraform plan -out=plan.tfplan

apply-dev:
  script:
    - cd tf/environments/development
    - terraform apply plan.tfplan
  only:
    - develop
...
```

This structure provides better control, security, and maintainability for your infrastructure deployments.
