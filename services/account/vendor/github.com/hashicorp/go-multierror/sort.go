package multierror

// <PERSON> implements sort.Interface function for length
func (err <PERSON>rror) Len() int {
	return len(err.<PERSON><PERSON><PERSON>)
}

// Swap implements sort.Interface function for swapping elements
func (err Error) Swap(i, j int) {
	err.<PERSON>rrors[i], err.<PERSON><PERSON><PERSON>[j] = err.<PERSON><PERSON><PERSON>[j], err.<PERSON><PERSON><PERSON>[i]
}

// Less implements sort.Interface function for determining order
func (err Error) Less(i, j int) bool {
	return err.Errors[i].Error() < err.Errors[j].<PERSON><PERSON>r()
}
