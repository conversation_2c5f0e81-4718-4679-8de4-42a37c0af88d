# github.com/cenkalti/backoff/v3 v3.0.0
## explicit; go 1.12
github.com/cenkalti/backoff/v3
# github.com/golang/snappy v0.0.1
## explicit
github.com/golang/snappy
# github.com/hashicorp/errwrap v1.0.0
## explicit
github.com/hashicorp/errwrap
# github.com/hashicorp/go-cleanhttp v0.5.1
## explicit
github.com/hashicorp/go-cleanhttp
# github.com/hashicorp/go-multierror v1.1.0
## explicit; go 1.14
github.com/hashicorp/go-multierror
# github.com/hashicorp/go-retryablehttp v0.6.6
## explicit; go 1.13
github.com/hashicorp/go-retryablehttp
# github.com/hashicorp/go-rootcerts v1.0.2
## explicit; go 1.12
github.com/hashicorp/go-rootcerts
# github.com/hashicorp/go-sockaddr v1.0.2
## explicit
github.com/hashicorp/go-sockaddr
# github.com/hashicorp/hcl v1.0.0
## explicit
github.com/hashicorp/hcl
github.com/hashicorp/hcl/hcl/ast
github.com/hashicorp/hcl/hcl/parser
github.com/hashicorp/hcl/hcl/scanner
github.com/hashicorp/hcl/hcl/strconv
github.com/hashicorp/hcl/hcl/token
github.com/hashicorp/hcl/json/parser
github.com/hashicorp/hcl/json/scanner
github.com/hashicorp/hcl/json/token
# github.com/hashicorp/vault/api v1.1.1
## explicit; go 1.13
github.com/hashicorp/vault/api
# github.com/hashicorp/vault/sdk v0.2.1
## explicit; go 1.16
github.com/hashicorp/vault/sdk/helper/compressutil
github.com/hashicorp/vault/sdk/helper/consts
github.com/hashicorp/vault/sdk/helper/hclutil
github.com/hashicorp/vault/sdk/helper/jsonutil
github.com/hashicorp/vault/sdk/helper/parseutil
github.com/hashicorp/vault/sdk/helper/strutil
# github.com/mitchellh/go-homedir v1.1.0
## explicit
github.com/mitchellh/go-homedir
# github.com/mitchellh/mapstructure v1.3.2
## explicit; go 1.14
github.com/mitchellh/mapstructure
# github.com/pierrec/lz4 v2.5.2+incompatible
## explicit
github.com/pierrec/lz4
github.com/pierrec/lz4/internal/xxh32
# github.com/ryanuber/go-glob v1.0.0
## explicit
github.com/ryanuber/go-glob
# golang.org/x/crypto v0.0.0-20200604202706-70a84ac30bf9
## explicit; go 1.11
golang.org/x/crypto/ed25519
golang.org/x/crypto/ed25519/internal/edwards25519
golang.org/x/crypto/pbkdf2
# golang.org/x/net v0.0.0-20200602114024-627f9648deb9
## explicit; go 1.11
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/hpack
golang.org/x/net/idna
# golang.org/x/text v0.3.2
## explicit
golang.org/x/text/secure/bidirule
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
# golang.org/x/time v0.0.0-20200416051211-89c76fbcd5d1
## explicit
golang.org/x/time/rate
# gopkg.in/square/go-jose.v2 v2.5.1
## explicit
gopkg.in/square/go-jose.v2
gopkg.in/square/go-jose.v2/cipher
gopkg.in/square/go-jose.v2/json
gopkg.in/square/go-jose.v2/jwt
